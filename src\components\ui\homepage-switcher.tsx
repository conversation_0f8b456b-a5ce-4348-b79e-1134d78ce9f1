import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Grid3X3, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

const HomepageSwitcher = () => {
  const location = useLocation();
  const isHome2 = location.pathname === '/home2';

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className="shadow-lg border-2 border-gray-200 bg-white/95 backdrop-blur-sm">
        <CardContent className="p-3">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs font-medium">
              Switch View
            </Badge>
            <div className="flex gap-1">
              <Button
                asChild
                variant={!isHome2 ? "default" : "ghost"}
                size="sm"
                className={cn(
                  "h-8 w-8 p-0",
                  !isHome2 && "bg-lawvriksh-navy hover:bg-lawvriksh-navy-dark"
                )}
              >
                <Link to="/" title="Portfolio Homepage">
                  <Home className="h-4 w-4" />
                </Link>
              </Button>
              <Button
                asChild
                variant={isHome2 ? "default" : "ghost"}
                size="sm"
                className={cn(
                  "h-8 w-8 p-0",
                  isHome2 && "bg-lawvriksh-navy hover:bg-lawvriksh-navy-dark"
                )}
              >
                <Link to="/home2" title="Content Hub">
                  <Grid3X3 className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HomepageSwitcher;
