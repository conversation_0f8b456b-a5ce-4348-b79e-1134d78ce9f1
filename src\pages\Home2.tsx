import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Search,
  Filter,
  Grid,
  List,
  TrendingUp,
  Clock,
  User,
  Eye,
  MessageSquare,
  BookOpen,
  FileText,
  Calendar,
  ArrowRight,
  Star,
  Bookmark,
  BarChart3,
  Scale,
  Gavel,
  Building2,
  Newspaper,
  Zap,
  Award,
  Users,
  Target,
  ChevronRight,
  Briefcase
} from 'lucide-react';
import AuthHeader from '@/components/AuthHeader';
import Footer from '@/components/Footer';
import ScrollToTop from '@/components/ScrollToTop';
import HomepageSwitcher from '@/components/ui/homepage-switcher';
import ContentDivision from '@/components/homepage/ContentDivision';
import ContentSidebar from '@/components/homepage/ContentSidebar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { contentApi, BlogPost, Note } from '@/services/api';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { getOptimizedImageUrl, handleImageError } from '@/utils/imageUtils';

const Home2 = () => {
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('latest');
  
  // Content state
  const [featuredContent, setFeaturedContent] = useState<BlogPost | null>(null);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [notes, setNotes] = useState<Note[]>([]);
  const [trendingContent, setTrendingContent] = useState<BlogPost[]>([]);

  // Content divisions for different categories
  const [corporateLawContent, setCorporateLawContent] = useState<BlogPost[]>([]);
  const [recentUpdatesContent, setRecentUpdatesContent] = useState<(BlogPost | Note)[]>([]);
  
  const { toast } = useToast();

  useEffect(() => {
    loadContent();
  }, []);

  const loadContent = async () => {
    try {
      setLoading(true);

      // Load featured content and recent posts
      const [blogsResponse, notesResponse] = await Promise.all([
        contentApi.getBlogPosts({ limit: 50, status: 'Active' }),
        contentApi.getNotes({ limit: 20, status: 'Active' })
      ]);

      const blogs = blogsResponse.blog_posts || [];
      const notesData = notesResponse.notes || [];

      // Set featured content (first featured blog or first blog)
      const featured = blogs.find(blog => blog.is_featured) || blogs[0];
      setFeaturedContent(featured);

      // Set other content
      setBlogPosts(blogs.filter(blog => blog.content_id !== featured?.content_id));
      setNotes(notesData);

      // Set trending content (top 6 most viewed/recent)
      setTrendingContent(blogs.slice(0, 6));

      // Organize content by categories for different divisions
      setCorporateLawContent(blogs.filter(blog =>
        blog.category?.toLowerCase().includes('corporate') ||
        blog.title.toLowerCase().includes('corporate') ||
        blog.summary?.toLowerCase().includes('corporate')
      ).slice(0, 6));

      // Recent updates - mix of recent blogs and notes
      const recentBlogs = blogs.slice(0, 8).map(blog => ({ ...blog, type: 'blog' as const }));
      const recentNotes = notesData.slice(0, 4).map(note => ({ ...note, type: 'note' as const }));
      setRecentUpdatesContent([...recentBlogs, ...recentNotes].sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      ).slice(0, 12));

    } catch (error) {
      console.error('Error loading content:', error);
      toast({
        title: "Error",
        description: "Failed to load content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort content
  const getFilteredContent = () => {
    let allContent: any[] = [];
    
    if (selectedCategory === 'all' || selectedCategory === 'blogs') {
      allContent = [...allContent, ...blogPosts.map(blog => ({ ...blog, type: 'blog' }))];
    }
    
    if (selectedCategory === 'all' || selectedCategory === 'notes') {
      allContent = [...allContent, ...notes.map(note => ({ ...note, type: 'note' }))];
    }

    // Apply search filter
    if (searchQuery) {
      allContent = allContent.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.summary && item.summary.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'latest':
        allContent.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'popular':
        allContent.sort((a, b) => (b.view_count || 0) - (a.view_count || 0));
        break;
      case 'alphabetical':
        allContent.sort((a, b) => a.title.localeCompare(b.title));
        break;
    }

    return allContent;
  };

  const renderContentCard = (item: any, type: string) => {
    const isNote = type === 'note';
    const linkTo = isNote ? `/notes/${item.note_id}` : `/blogs/${item.content_id}`;
    
    if (viewMode === 'list') {
      return (
        <Card key={`${type}-${item.content_id || item.note_id}`} className="group hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-lawvriksh-navy/20 overflow-hidden">
          <Link to={linkTo} className="block">
            <CardContent className="p-4 overflow-hidden">
              <div className="flex gap-4 overflow-hidden">
                {/* Thumbnail */}
                <div className="flex-shrink-0 w-20 h-14 rounded overflow-hidden bg-gray-100">
                  <img
                    src={getOptimizedImageUrl(item.featured_image, 160, 112, type)}
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => handleImageError(e, type)}
                  />
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary" className="text-xs font-medium">
                      {isNote ? 'Note' : 'Article'}
                    </Badge>
                    {item.category && (
                      <Badge variant="outline" className="text-xs">
                        {item.category}
                      </Badge>
                    )}
                  </div>

                  <h3 className="font-semibold text-sm line-clamp-1 group-hover:text-lawvriksh-navy transition-colors mb-1">
                    {item.title}
                  </h3>

                  {item.summary && (
                    <p className="text-xs text-gray-600 line-clamp-1 mb-2">
                      {item.summary}
                    </p>
                  )}

                  <div className="flex items-center gap-3 text-xs text-gray-500 overflow-hidden">
                    <span className="flex items-center gap-1 min-w-0 flex-1 overflow-hidden">
                      <User className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{item.author_name || 'Anonymous'}</span>
                    </span>
                    <span className="flex items-center gap-1 flex-shrink-0 whitespace-nowrap">
                      <Calendar className="h-3 w-3" />
                      {format(new Date(item.created_at), 'MMM dd')}
                    </span>
                    {item.view_count && (
                      <span className="flex items-center gap-1 flex-shrink-0 whitespace-nowrap">
                        <Eye className="h-3 w-3" />
                        {item.view_count}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Link>
        </Card>
      );
    }

    // Grid view
    return (
      <Card key={`${type}-${item.content_id || item.note_id}`} className="group hover:shadow-md transition-all duration-300 h-full border border-gray-200 hover:border-lawvriksh-navy/20 overflow-hidden">
        <Link to={linkTo} className="block h-full">
          {/* Thumbnail */}
          <div className="aspect-[16/10] overflow-hidden bg-gray-100">
            <img
              src={getOptimizedImageUrl(item.featured_image, 350, 220, type)}
              alt={item.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
              onError={(e) => handleImageError(e, type)}
            />
          </div>

          <CardContent className="p-4 flex flex-col h-[calc(100%-220px)] overflow-hidden">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="secondary" className="text-xs font-medium">
                  {isNote ? 'Note' : 'Article'}
                </Badge>
                {item.category && (
                  <Badge variant="outline" className="text-xs">
                    {item.category}
                  </Badge>
                )}
              </div>

              <h3 className="font-semibold text-sm line-clamp-2 group-hover:text-lawvriksh-navy transition-colors mb-2 leading-tight">
                {item.title}
              </h3>

              {item.summary && (
                <p className="text-xs text-gray-600 line-clamp-2 mb-3 leading-relaxed">
                  {item.summary}
                </p>
              )}
            </div>

            <div className="mt-auto pt-2 border-t border-gray-100">
              <div className="flex items-center text-xs text-gray-500 gap-2 overflow-hidden">
                <div className="flex items-center gap-1 min-w-0 flex-1 overflow-hidden">
                  <User className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate text-xs">{item.author_name || 'Anonymous'}</span>
                </div>
                <span className="flex-shrink-0 whitespace-nowrap text-xs ml-auto">
                  {format(new Date(item.created_at), 'MMM dd')}
                </span>
              </div>
            </div>
          </CardContent>
        </Link>
      </Card>
    );
  };

  const filteredContent = getFilteredContent();



  return (
    <div className="min-h-screen bg-white">
      <AuthHeader />
      
      <main className="pt-20">
        {/* Streamlined Search Header */}
        <section className="bg-white border-b border-gray-200 py-4">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-5xl mx-auto">
              <div className="flex flex-col lg:flex-row gap-3">
                {/* Search */}
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search legal content..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 h-10 border-gray-300 focus:border-lawvriksh-navy"
                  />
                </div>

                {/* Filters Row */}
                <div className="flex gap-2">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-36 h-10">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Content</SelectItem>
                      <SelectItem value="blogs">Articles</SelectItem>
                      <SelectItem value="notes">Notes</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-32 h-10">
                      <SelectValue placeholder="Sort" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="latest">Latest</SelectItem>
                      <SelectItem value="popular">Popular</SelectItem>
                      <SelectItem value="alphabetical">A-Z</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* View Mode */}
                  <div className="flex border border-gray-300 rounded-md">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="h-10 px-3 rounded-r-none border-0"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                      className="h-10 px-3 rounded-l-none border-0"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Content Section - Hero Style */}
        {featuredContent && !loading && (
          <section className="py-6 bg-gradient-to-r from-lawvriksh-navy/5 to-lawvriksh-burgundy/5">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl border border-gray-200 hover:border-lawvriksh-navy/30 overflow-hidden bg-white">
                <Link to={`/blogs/${featuredContent.content_id}`} className="block">
                  <div className="grid grid-cols-1 lg:grid-cols-5 gap-0">
                    {/* Featured Image */}
                    <div className="lg:col-span-2 relative aspect-[16/10] lg:aspect-[4/3] overflow-hidden">
                      <img
                        src={getOptimizedImageUrl(featuredContent.featured_image, 500, 350, 'blog')}
                        alt={featuredContent.title}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                        onError={(e) => handleImageError(e, 'blog')}
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-lawvriksh-gold text-lawvriksh-navy font-medium text-sm px-3 py-1">
                          <Star className="h-3 w-3 mr-1" />
                          Featured
                        </Badge>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="lg:col-span-3 p-8">
                      <div className="flex items-center gap-3 mb-4">
                        <Badge variant="secondary" className="text-sm">
                          {featuredContent.category || 'Article'}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {format(new Date(featuredContent.created_at), 'MMM dd, yyyy')}
                        </span>
                      </div>

                      <h3 className="legal-heading text-2xl font-bold text-lawvriksh-navy mb-4 group-hover:text-lawvriksh-burgundy transition-colors line-clamp-2">
                        {featuredContent.title}
                      </h3>

                      {featuredContent.summary && (
                        <p className="legal-text text-gray-600 mb-6 line-clamp-3 text-base leading-relaxed">
                          {featuredContent.summary}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            {featuredContent.author_name}
                          </span>
                          {featuredContent.comment_count > 0 && (
                            <span className="flex items-center gap-2">
                              <MessageSquare className="h-4 w-4" />
                              {featuredContent.comment_count}
                            </span>
                          )}
                          {featuredContent.view_count && (
                            <span className="flex items-center gap-2">
                              <Eye className="h-4 w-4" />
                              {featuredContent.view_count}
                            </span>
                          )}
                        </div>

                        <Button variant="default" size="sm" className="bg-lawvriksh-navy hover:bg-lawvriksh-burgundy text-white">
                          Read Article
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Link>
              </Card>
            </div>
          </section>
        )}

        {/* Diversified Content Layout */}
        {searchQuery ? (
          /* Search Results Section */
          <section className="py-8 bg-white">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Search Results */}
                <div className="lg:col-span-3">
                  {!loading && (
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-2">
                        <h2 className="legal-heading text-lg font-semibold text-lawvriksh-navy">
                          Search Results
                        </h2>
                        <Badge variant="outline" className="text-xs">
                          {filteredContent.length} {filteredContent.length === 1 ? 'item' : 'items'}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSearchQuery('')}
                        className="text-xs"
                      >
                        Clear Search
                      </Button>
                    </div>
                  )}

                  {loading ? (
                    <div className={cn(
                      viewMode === 'grid'
                        ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"
                        : "space-y-3"
                    )}>
                      {Array.from({ length: 9 }).map((_, i) => (
                        <Card key={i} className="h-64">
                          <Skeleton className="w-full h-40" />
                          <CardContent className="p-4">
                            <Skeleton className="h-4 w-3/4 mb-2" />
                            <Skeleton className="h-3 w-full mb-1" />
                            <Skeleton className="h-3 w-2/3" />
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : filteredContent.length > 0 ? (
                    <div className={cn(
                      viewMode === 'grid'
                        ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"
                        : "space-y-3"
                    )}>
                      {filteredContent.map((item) => renderContentCard(item, item.type))}
                    </div>
                  ) : (
                    <div className="text-center py-16 bg-gray-50 rounded-lg">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-600 mb-2">No content found</h3>
                      <p className="text-gray-500 text-sm">
                        No results found for "{searchQuery}". Try different keywords.
                      </p>
                    </div>
                  )}
                </div>

                {/* Sidebar for Search Results */}
                <div className="lg:col-span-1">
                  <Card className="border border-gray-200">
                    <CardHeader className="pb-2">
                      <h3 className="legal-heading text-sm font-semibold text-lawvriksh-navy flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        Search Stats
                      </h3>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex items-center justify-between py-1">
                        <span className="text-xs text-gray-600">Total Results</span>
                        <Badge variant="secondary" className="text-xs">
                          {filteredContent.length}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between py-1">
                        <span className="text-xs text-gray-600">Articles</span>
                        <Badge variant="outline" className="text-xs">
                          {filteredContent.filter(item => item.type === 'blog').length}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between py-1">
                        <span className="text-xs text-gray-600">Notes</span>
                        <Badge variant="outline" className="text-xs">
                          {filteredContent.filter(item => item.type === 'note').length}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </section>
        ) : (
          /* Main Diversified Content Layout */
          <div className="space-y-12 py-8">
            {/* Corporate Law Section */}
            <section className="bg-white">
              <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <ContentDivision
                  title="Corporate Law"
                  content={corporateLawContent}
                  icon={Building2}
                  viewAllLink="/blogs?category=Corporate Law"
                  emptyMessage="No corporate law content available"
                  maxItems={6}
                  loading={loading}
                  renderContentCard={renderContentCard}
                />
              </div>
            </section>

            {/* Recent Updates Section */}
            <section className="bg-gradient-to-r from-lawvriksh-navy/5 to-lawvriksh-burgundy/5 py-8">
              <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <ContentDivision
                  title="Recent Updates"
                  content={recentUpdatesContent}
                  icon={Zap}
                  viewAllLink="/blogs"
                  emptyMessage="No recent updates available"
                  maxItems={12}
                  loading={loading}
                  renderContentCard={renderContentCard}
                />
              </div>
            </section>

            {/* Trending Topics Section */}
            <section className="bg-white">
              <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                  {/* Main Trending Content */}
                  <div className="lg:col-span-3">
                    <ContentDivision
                      title="Trending Topics"
                      content={trendingContent}
                      icon={TrendingUp}
                      viewAllLink="/blogs"
                      emptyMessage="No trending content available"
                      maxItems={6}
                      loading={loading}
                      renderContentCard={renderContentCard}
                    />
                  </div>

                  {/* Sidebar with Quick Stats and Popular Content */}
                  <div className="lg:col-span-1">
                    <ContentSidebar
                      trendingContent={trendingContent}
                      blogPostsCount={blogPosts.length + (featuredContent ? 1 : 0)}
                      notesCount={notes.length}
                      corporateLawCount={corporateLawContent.length}
                      legalNewsCount={0}
                      todayContentCount={recentUpdatesContent.filter(item => {
                        const today = new Date();
                        const itemDate = new Date(item.created_at);
                        return itemDate.toDateString() === today.toDateString();
                      }).length}
                      loading={loading}
                    />
                  </div>
                </div>
              </div>
            </section>
          </div>
        )}
      </main>

      <Footer />
      <ScrollToTop />
      <HomepageSwitcher />
    </div>
  );
};

export default Home2;
