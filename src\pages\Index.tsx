
import React, { useEffect } from 'react';
import AuthHeader from '@/components/AuthHeader';
import ProfessionalHero from '@/components/homepage/ModernHero';
import AboutUsPreview from '@/components/homepage/AboutUsPreview';
import PracticeAreas from '@/components/homepage/PracticeAreas';
import FeaturedContent from '@/components/homepage/FeaturedContent';
import ContentHub from '@/components/homepage/ContentHub';
import QuickContentPreview from '@/components/homepage/QuickContentPreview';
import UnifiedSearch from '@/components/homepage/UnifiedSearch';
import StrategicCTA from '@/components/homepage/StrategicCTA';
import ServiceShowcase from '@/components/homepage/ServiceShowcase';
import StatsSection from '@/components/homepage/StatsSection';
import CallToAction from '@/components/homepage/CallToAction';
import Footer from '@/components/Footer';
import ScrollToTop from '@/components/ScrollToTop';
import HomepageSwitcher from '@/components/ui/homepage-switcher';

const Index = () => {

  // Update page title and meta description
  useEffect(() => {
    document.title = "LawVriksh | Rooted in Excellence, Growing Your Trust";

    // Add meta description for SEO
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 'LawVriksh - A professional law firm rooted in excellence and growing your trust. Expert legal services with a commitment to tradition, authority, and client success.');
    } else {
      const meta = document.createElement('meta');
      meta.name = 'description';
      meta.content = 'LawVriksh - A professional law firm rooted in excellence and growing your trust. Expert legal services with a commitment to tradition, authority, and client success.';
      document.head.appendChild(meta);
    }
  }, []);

  return (
    <div className="homepage-container min-h-screen bg-white relative">
      <AuthHeader />
      <main>
        <ProfessionalHero />

        {/* Unified Search Section */}
        <section className="py-12 bg-gradient-to-br from-lawvriksh-navy/5 to-lawvriksh-burgundy/5">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8">
              <h2 className="legal-heading text-2xl sm:text-3xl font-bold text-lawvriksh-navy mb-4">
                Find Legal Content Instantly
              </h2>
              <p className="legal-text max-w-2xl mx-auto">
                Search through thousands of articles, case notes, and professional insights
              </p>
            </div>
            <div className="max-w-4xl mx-auto">
              <UnifiedSearch />
            </div>
          </div>
        </section>

        <AboutUsPreview />

        {/* Strategic CTA after About Us */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
              <StrategicCTA variant="consultation" />
              <StrategicCTA variant="resources" />
            </div>
          </div>
        </section>

        <PracticeAreas />

        {/* Strategic CTA after Practice Areas */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <StrategicCTA variant="primary" />
            </div>
          </div>
        </section>

        <FeaturedContent />
        <QuickContentPreview />
        <ContentHub />
        <ServiceShowcase />
        <StatsSection />

        {/* Strategic CTA before final CTA */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto">
              <StrategicCTA variant="contact" />
            </div>
          </div>
        </section>

        <CallToAction />
      </main>
      <Footer />
      <ScrollToTop />
      <HomepageSwitcher />
    </div>
  );
};

export default Index;
