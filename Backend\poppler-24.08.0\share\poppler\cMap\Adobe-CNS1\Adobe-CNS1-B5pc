%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%IncludeResource: ProcSet (CIDInit)
%%BeginResource: CMap (Adobe-CNS1-B5pc)
%%Title: (Adobe-CNS1-B5pc Adobe B5pc 0)
%%Version: 4.006
%%Copyright: -----------------------------------------------------------
%%Copyright: Copyright 1990-2019 Adobe. All rights reserved.
%%Copyright:
%%Copyright: Redistribution and use in source and binary forms, with or
%%Copyright: without modification, are permitted provided that the
%%Copyright: following conditions are met:
%%Copyright:
%%Copyright: Redistributions of source code must retain the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer.
%%Copyright:
%%Copyright: Redistributions in binary form must reproduce the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer in the documentation and/or other materials
%%Copyright: provided with the distribution. 
%%Copyright:
%%Copyright: Neither the name of Adobe nor the names of its contributors
%%Copyright: may be used to endorse or promote products derived from
%%Copyright: this software without specific prior written permission.
%%Copyright:
%%Copyright: THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
%%Copyright: CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
%%Copyright: INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
%%Copyright: MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
%%Copyright: DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
%%Copyright: CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
%%Copyright: SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
%%Copyright: NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
%%Copyright: LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
%%Copyright: HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
%%Copyright: CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
%%Copyright: OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
%%Copyright: SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
%%Copyright: -----------------------------------------------------------
%%EndComments

/CIDInit /ProcSet findresource begin

12 dict begin

begincmap

/CIDSystemInfo 3 dict dup begin
/Registry (Adobe) def
/Ordering (Adobe_CNS1_B5pc) def
/Supplement 0 def
end def

/CMapName /Adobe-CNS1-B5pc def

/CMapVersion 4.006 def
/CMapType 1 def

/XUID [1 10 25335 1212] def

/WMode 0 def

1 begincodespacerange
<0000> <FFFF>
endcodespacerange

1 beginbfrange
<0000> <0000> <20>
endbfrange

% Adobe-CNS1	
% B5pc (Macintosh)	
100 beginbfrange
<0001> <005F>	<20>
<0060> <0062>	<FD>
<0063> <007B>	<A140>
<0080> <00A1>	<A15D>
<00A2> <00F6>	<A1A1>
<00F7> <00F7>	<A1F7>
<00F8> <00F8>	<A1F6>
<00F9> <00FF>	<A1F8>
<0100> <013E>	<A240>
<013F> <019C>	<A2A1>
<019D> <01DB>	<A340>
<01DC> <01F6>	<A3A1>
<01F7> <01F9>	<A3BD>
<0232> <0252>	<A3C0>
<0253> <0291>	<A440>
<0292> <02EF>	<A4A1>
<02F0> <02FF>	<A540>
<0300> <032E>	<A550>
<032F> <038C>	<A5A1>
<038D> <03CB>	<A640>
<03CC> <03FF>	<A6A1>
<0400> <0429>	<A6D5>
<042A> <0468>	<A740>
<0469> <04C6>	<A7A1>
<04C7> <04FF>	<A840>
<0500> <0505>	<A879>
<0506> <0563>	<A8A1>
<0564> <05A2>	<A940>
<05A3> <05FF>	<A9A1>
<0600> <0600>	<A9FE>
<0601> <063F>	<AA40>
<0640> <069D>	<AAA1>
<069E> <06DC>	<AB40>
<06DD> <06FF>	<ABA1>
<0700> <073A>	<ABC4>
<073B> <0779>	<AC40>
<077A> <07D6>	<ACA1>
<07D7> <07FF>	<AD40>
<0800> <0815>	<AD69>
<0816> <0873>	<ADA1>
<0874> <08B2>	<AE40>
<08B3> <08FF>	<AEA1>
<0900> <0910>	<AEEE>
<0911> <094F>	<AF40>
<0950> <097E>	<AFA1>
<097F> <097F>	<ACFE>
<0980> <09AE>	<AFD0>
<09AF> <09ED>	<B040>
<09EE> <09FF>	<B0A1>
<0A00> <0A4B>	<B0B3>
<0A4C> <0A8A>	<B140>
<0A8B> <0AE8>	<B1A1>
<0AE9> <0AFF>	<B240>
<0B00> <0B27>	<B257>
<0B28> <0B85>	<B2A1>
<0B86> <0BC4>	<B340>
<0BC5> <0BFF>	<B3A1>
<0C00> <0C22>	<B3DC>
<0C23> <0C61>	<B440>
<0C62> <0CBF>	<B4A1>
<0CC0> <0CFE>	<B540>
<0CFF> <0CFF>	<B5A1>
<0D00> <0D5C>	<B5A2>
<0D5D> <0D9B>	<B640>
<0D9C> <0DF9>	<B6A1>
<0DFA> <0DFF>	<B740>
<0E00> <0E38>	<B746>
<0E39> <0E96>	<B7A1>
<0E97> <0ED5>	<B840>
<0ED6> <0EFF>	<B8A1>
<0F00> <0F33>	<B8CB>
<0F34> <0F72>	<B940>
<0F73> <0FD0>	<B9A1>
<0FD1> <0FFF>	<BA40>
<1000> <100F>	<BA6F>
<1010> <106D>	<BAA1>
<106E> <10AC>	<BB40>
<10AD> <10D3>	<BBA1>
<10D4> <10D4>	<BE52>
<10D5> <10FF>	<BBC8>
<1100> <110B>	<BBF3>
<110C> <114A>	<BC40>
<114B> <11A8>	<BCA1>
<11A9> <11E7>	<BD40>
<11E8> <11FF>	<BDA1>
<1200> <1245>	<BDB9>
<1246> <1257>	<BE40>
<1258> <1283>	<BE53>
<1284> <12E1>	<BEA1>
<12E2> <12FF>	<BF40>
<1300> <1320>	<BF5E>
<1321> <137E>	<BFA1>
<137F> <13BD>	<C040>
<13BE> <13FF>	<C0A1>
<1400> <141B>	<C0E3>
<141C> <145A>	<C140>
<145B> <1464>	<C1A1>
<1465> <1465>	<C2CB>
<1466> <14B9>	<C1AB>
<14BA> <14F8>	<C240>
endbfrange
100 beginbfrange
<14F9> <14FF>	<C2A1>
<1500> <1522>	<C2A8>
<1523> <1555>	<C2CC>
<1556> <1576>	<C340>
<1577> <1577>	<C456>
<1578> <1595>	<C361>
<1596> <15AD>	<C3A1>
<15AE> <15AE>	<C3BA>
<15AF> <15AF>	<C3B9>
<15B0> <15F3>	<C3BB>
<15F4> <15FF>	<C440>
<1600> <1609>	<C44C>
<160A> <1631>	<C457>
<1632> <168F>	<C4A1>
<1690> <16CE>	<C540>
<16CF> <16FF>	<C5A1>
<1700> <172C>	<C5D2>
<172D> <176B>	<C640>
<176C> <1775>	<C940>
<1776> <1796>	<C94B>
<1797> <1797>	<C9BE>
<1798> <17AA>	<C96C>
<17AB> <17C7>	<C9A1>
<17C8> <17F5>	<C9BF>
<17F6> <17F6>	<CAF7>
<17F7> <17FF>	<C9ED>
<1800> <1808>	<C9F6>
<1809> <1847>	<CA40>
<1848> <189D>	<CAA1>
<189E> <18A4>	<CAF8>
<18A5> <18E3>	<CB40>
<18E4> <18FF>	<CBA1>
<1900> <1941>	<CBBD>
<1942> <1980>	<CC40>
<1981> <19DE>	<CCA1>
<19DF> <19FF>	<CD40>
<1A00> <1A1D>	<CD61>
<1A1E> <1A7B>	<CDA1>
<1A7C> <1ABA>	<CE40>
<1ABB> <1AFF>	<CEA1>
<1B00> <1B18>	<CEE6>
<1B19> <1B57>	<CF40>
<1B58> <1BB5>	<CFA1>
<1BB6> <1BF4>	<D040>
<1BF5> <1BFF>	<D0A1>
<1C00> <1C52>	<D0AC>
<1C53> <1C91>	<D140>
<1C92> <1CEF>	<D1A1>
<1CF0> <1CFF>	<D240>
<1D00> <1D2E>	<D250>
<1D2F> <1D8C>	<D2A1>
<1D8D> <1DCB>	<D340>
<1DCC> <1DFF>	<D3A1>
<1E00> <1E29>	<D3D5>
<1E2A> <1E68>	<D440>
<1E69> <1EC6>	<D4A1>
<1EC7> <1EFF>	<D540>
<1F00> <1F05>	<D579>
<1F06> <1F63>	<D5A1>
<1F64> <1FA2>	<D640>
<1FA3> <1FCD>	<D6A1>
<1FCE> <1FCE>	<DADF>
<1FCF> <1FFF>	<D6CD>
<2000> <2000>	<D6FE>
<2001> <203A>	<D740>
<203B> <203E>	<D77B>
<203F> <209C>	<D7A1>
<209D> <20DB>	<D840>
<20DC> <20FF>	<D8A1>
<2100> <2139>	<D8C5>
<213A> <2178>	<D940>
<2179> <21D6>	<D9A1>
<21D7> <21FF>	<DA40>
<2200> <2215>	<DA69>
<2216> <2253>	<DAA1>
<2254> <2254>	<D6CC>
<2255> <2273>	<DAE0>
<2274> <22B2>	<DB40>
<22B3> <22B8>	<DBA1>
<22B9> <22B9>	<D77A>
<22BA> <22FF>	<DBA7>
<2300> <2311>	<DBED>
<2312> <2350>	<DC40>
<2351> <23AE>	<DCA1>
<23AF> <23ED>	<DD40>
<23EE> <23FF>	<DDA1>
<2400> <2448>	<DDB3>
<2449> <244A>	<DDFD>
<244B> <2489>	<DE40>
<248A> <24E7>	<DEA1>
<24E8> <24FF>	<DF40>
<2500> <2526>	<DF58>
<2527> <2584>	<DFA1>
<2585> <25C3>	<E040>
<25C4> <25FF>	<E0A1>
<2600> <2621>	<E0DD>
<2622> <2660>	<E140>
<2661> <26BE>	<E1A1>
<26BF> <26FD>	<E240>
<26FE> <26FF>	<E2A1>
endbfrange
97 beginbfrange
<2700> <275B>	<E2A3>
<275C> <279A>	<E340>
<279B> <27F8>	<E3A1>
<27F9> <27FF>	<E440>
<2800> <2837>	<E447>
<2838> <2895>	<E4A1>
<2896> <28D4>	<E540>
<28D5> <28FF>	<E5A1>
<2900> <2932>	<E5CC>
<2933> <2971>	<E640>
<2972> <29CF>	<E6A1>
<29D0> <29FF>	<E740>
<2A00> <2A0E>	<E770>
<2A0F> <2A6C>	<E7A1>
<2A6D> <2AAB>	<E840>
<2AAC> <2AAD>	<E8A1>
<2AAE> <2AAE>	<EBF1>
<2AAF> <2AFF>	<E8A3>
<2B00> <2B0A>	<E8F4>
<2B0B> <2B40>	<E940>
<2B41> <2B41>	<ECDE>
<2B42> <2B4A>	<E976>
<2B4B> <2BA8>	<E9A1>
<2BA9> <2BE7>	<EA40>
<2BE8> <2BFF>	<EAA1>
<2C00> <2C45>	<EAB9>
<2C46> <2C60>	<EB40>
<2C61> <2C61>	<F0CB>
<2C62> <2C85>	<EB5B>
<2C86> <2CD5>	<EBA1>
<2CD6> <2CE2>	<EBF2>
<2CE3> <2CFF>	<EC40>
<2D00> <2D21>	<EC5D>
<2D22> <2D5E>	<ECA1>
<2D5F> <2D7E>	<ECDF>
<2D7F> <2DBD>	<ED40>
<2DBE> <2DC6>	<EDA1>
<2DC7> <2DC7>	<F056>
<2DC8> <2DFF>	<EDAA>
<2E00> <2E1C>	<EDE2>
<2E1D> <2E5B>	<EE40>
<2E5C> <2EA5>	<EEA1>
<2EA6> <2EB8>	<EEEC>
<2EB9> <2EF7>	<EF40>
<2EF8> <2EFF>	<EFA1>
<2F00> <2F55>	<EFA9>
<2F56> <2F6B>	<F040>
<2F6C> <2F93>	<F057>
<2F94> <2FBD>	<F0A1>
<2FBE> <2FF0>	<F0CC>
<2FF1> <2FFF>	<F140>
<3000> <3013>	<F14F>
<3014> <3014>	<EEEB>
<3015> <301C>	<F163>
<301D> <302F>	<F16C>
<3030> <308D>	<F1A1>
<308E> <30B5>	<F240>
<30B6> <30CB>	<F269>
<30CC> <30ED>	<F2A1>
<30EE> <30EE>	<F4B5>
<30EF> <30FF>	<F2C3>
<3100> <312A>	<F2D4>
<312B> <315F>	<F340>
<3160> <3160>	<F16B>
<3161> <316A>	<F375>
<316B> <31C8>	<F3A1>
<31C9> <31EE>	<F440>
<31EF> <31EF>	<F268>
<31F0> <31FF>	<F466>
<3200> <3208>	<F476>
<3209> <321C>	<F4A1>
<321D> <3263>	<F4B6>
<3264> <3264>	<F663>
<3265> <3266>	<F4FD>
<3267> <32A5>	<F540>
<32A6> <32FF>	<F5A1>
<3300> <3303>	<F5FB>
<3304> <3326>	<F640>
<3327> <3341>	<F664>
<3342> <339F>	<F6A1>
<33A0> <33DE>	<F740>
<33DF> <33FF>	<F7A1>
<3400> <343C>	<F7C2>
<343D> <347B>	<F840>
<347C> <34D9>	<F8A1>
<34DA> <34FF>	<F940>
<3500> <3510>	<F966>
<3511> <3511>	<F9C4>
<3512> <3519>	<F977>
<351A> <353C>	<F9A1>
<353D> <353D>	<F9C5>
<353E> <3548>	<F9C7>
<3549> <3549>	<F9C6>
<354A> <354D>	<F9D2>
<354E> <354E>	<A14B>
<354F> <354F>	<A1E3>
<35AF> <35B2>	<A159>
endbfrange

endcmap
CMapName currentdict /CMap defineresource pop
end
end

%%EndResource
%%EOF
