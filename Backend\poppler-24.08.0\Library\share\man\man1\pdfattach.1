.\" Copyright 2019 Albert Astals Cid
.TH pdfattach 1 "10 Febuary 2019"
.SH NAME
pdfattach \- Portable Document Format (PDF) document embedded file
creator (version 3.03)
.SH SYNOPSIS
.B pdfattach
[options]
.I input-PDF-file file-to-attach output-PDF-file
.SH DESCRIPTION
.B Pdfattach
adds a new embedded file (attachment) to an existing Portable
Document Format (PDF) file.
.SH OPTIONS
.TP
.B \-replace
Replace embedded file with same name (if it exists)
.TP
.B \-v
Print copyright and version information.
.TP
.B \-h
Print usage information.
.RB ( \-help
and
.B \-\-help
are equivalent.)
.SH EXIT CODES
.TP
0
No error.
.TP
1
Error opening input PDF file.
.TP
2
Error opening file to attach.
.TP
3
Output file already exists.
.TP
3
There is already an attached file with that name.
.TP
5
Error saving the output file.
.SH AUTHOR
The pdfattach software and documentation are copyright 2019 The Poppler developers
.SH "SEE ALSO"
.BR pdfdetach (1),
.BR pdfimages (1),
.BR pdfinfo (1),
.BR pdftocairo (1),
.<PERSON> pdftohtml (1),
.BR pdftoppm (1),
.BR pdftops (1),
.BR pdftotext (1)
.BR pdfseparate (1),
.BR pdfsig (1),
.BR pdfunite (1)
