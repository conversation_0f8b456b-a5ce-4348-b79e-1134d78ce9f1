/* This file is part of Libspectre.
 *
 * Copyright (C) 2007 <PERSON> <<EMAIL>>
 * Copyright (C) 2007 <PERSON> <<EMAIL>>
 *
 * Libspectre is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2, or (at your option)
 * any later version.
 *
 * Libspectre is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */

/* This function comes from spectre-utils from libspectre */

#ifndef GSTRTOD_H
#define GSTRTOD_H

#include "poppler_private_export.h"

#ifdef __cplusplus
extern "C" {
#endif

/* This function behaves like the standard atof()/(strtod() function
 * does in the C locale. It does this without actually changing
 * the current locale, since that would not be thread-safe.
 * A limitation of the implementation is that this function
 * will still accept localized versions of infinities and NANs.
 */
double POPPLER_PRIVATE_EXPORT gatof(const char *nptr);
double gstrtod(const char *nptr, char **endptr);

#ifdef __cplusplus
}
#endif

#endif
