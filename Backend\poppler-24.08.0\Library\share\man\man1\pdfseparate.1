.\" Copyright 2011 The Poppler Developers - http://poppler.freedesktop.org
.TH pdfseparate 1 "15 September 2011"
.SH NAME
pdfseparate \- Portable Document Format (PDF) page extractor
.SH SYNOPSIS
.B pdfseparate
[options]
.I PDF-file PDF-page-pattern
.SH DESCRIPTION
.B pdfseparate
extract single pages from a Portable Document Format (PDF).
.PP
pdfseparate reads the PDF file
.IR PDF-file ,
extracts one or more pages, and writes one PDF file for each page to
.IR PDF-page-pattern.
.PP
PDF-page-pattern should contain
.BR %d
(or any variant respecting printf format), since %d is replaced by the page number.
.TP
The PDF-file should not be encrypted.
.SH OPTIONS
.TP
.BI \-f " number"
Specifies the first page to extract. If \-f is omitted, extraction starts with page 1.
.TP
.BI \-l " number"
Specifies the last page to extract. If \-l is omitted, extraction ends with the last page.
.TP
.B \-v
Print copyright and version information.
.TP
.B \-h
Print usage information.
.RB ( \-help
and
.B \-\-help
are equivalent.)
.SH EXAMPLE
pdfseparate sample.pdf sample-%d.pdf
.TP
extracts all pages from sample.pdf, if i.e. sample.pdf has 3 pages, it produces
.TP
sample-1.pdf, sample-2.pdf, sample-3.pdf
.SH AUTHOR
The pdfseparate software and documentation are copyright 1996-2004 Glyph
& Cog, LLC and copyright 2005-2011 The Poppler Developers - http://poppler.freedesktop.org
.SH "SEE ALSO"
.BR pdfdetach (1),
.BR pdffonts (1),
.BR pdfimages (1),
.BR pdfinfo (1),
.BR pdftocairo (1),
.BR pdftohtml (1),
.BR pdftoppm (1),
.BR pdftops (1),
.BR pdftotext (1)
.BR pdfsig (1),
.BR pdfunite (1)
